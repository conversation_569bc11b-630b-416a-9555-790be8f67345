#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
multi_doc_rag.py
- Çoklu .txt/.pdf dokümandan metni okur
- Chunk'lar ve metadata üretir
- Similarity detection ile duplicate content'leri tespit eder
- Hybrid retrieval (BM25 + Dense FAISS) + RRF fusion uygular
- Document diversity ile balanced results sağlar
- Multilingual semantic reranking (CrossEncoder yerine) ile doğru sonuçlar

Kullanım:
  python multi_doc_rag.py --docs-folder ./documents --query "sentry nedir?" \
      --chunk-size 300 --overlap 50 --topk 50 --final-k 5
"""

import argparse
import json
import sys
import glob
from datetime import datetime
from pathlib import Path

import numpy as np

# PDF okumak için
from pypdf import PdfReader

# BM25 (lexical)
from rank_bm25 import BM25Okapi

# Dense embedding + FAISS index
import faiss
from sentence_transformers import SentenceTransformer, CrossEncoder

# ----------------------------
# 1) IO: Dosyalardan metin çekme
# ----------------------------

def read_text(path: Path) -> str:
    if not path.exists():
        raise FileNotFoundError(f"Dosya bulunamadı: {path}")

    if path.suffix.lower() == ".txt":
        return path.read_text(encoding="utf-8", errors="ignore")

    if path.suffix.lower() == ".pdf":
        reader = PdfReader(str(path))
        texts = []
        for page in reader.pages:
            t = page.extract_text() or ""
            texts.append(t)
        return "\n".join(texts)

    raise ValueError(f"Desteklenmeyen dosya türü: {path.suffix} (yalnızca .txt ve .pdf)")

def load_multiple_documents(docs_path):
    """
    Çoklu dosya yükleme - folder veya tek dosya destekler
    """
    docs_path = Path(docs_path)
    documents = {}

    if docs_path.is_file():
        # Tek dosya
        doc_id = docs_path.stem
        text = read_text(docs_path)
        documents[doc_id] = text
        print(f"Yüklendi: {doc_id} ({len(text)} karakter)")

    elif docs_path.is_dir():
        # Folder içindeki tüm PDF/TXT dosyalar
        patterns = ["*.pdf", "*.txt"]
        for pattern in patterns:
            for file_path in docs_path.glob(pattern):
                doc_id = file_path.stem
                try:
                    text = read_text(file_path)
                    documents[doc_id] = text
                    print(f"Yüklendi: {doc_id} ({len(text)} karakter)")
                except Exception as e:
                    print(f"Hata - {file_path}: {e}")
    else:
        raise ValueError(f"Geçersiz path: {docs_path}")

    return documents

# -----------------------------------
# 2) Geliştirilmiş chunking (cümle sınırlarına saygılı)
# -----------------------------------

import re

_SENT_SPLIT = re.compile(r'(?<=[.!?])\s+')

def chunk_text(text: str, chunk_size_words: int = 300, overlap_words: int = 50,
               min_chars: int = 120):
    """
    Cümle sınırlarına saygılı chunking - cümle ortasından kesmez
    """
    # Cümlelere böl
    sents = [s.strip() for s in _SENT_SPLIT.split(text) if s.strip()]
    chunks, starts = [], []
    current_chunk = []
    current_word_count = 0

    word_position = 0  # Toplam kelime pozisyonu takibi

    for sent in sents:
        sent_words = sent.split()
        sent_word_count = len(sent_words)

        # Eğer bu cümleyi eklemek chunk boyutunu aşarsa
        if current_word_count + sent_word_count > chunk_size_words and current_chunk:
            # Mevcut chunk'ı kaydet
            chunk_text = " ".join(current_chunk).strip()
            if len(chunk_text) >= min_chars:  # Çok kısa chunk'ları önle
                chunks.append(chunk_text)
                starts.append(word_position - current_word_count)

            # Yeni chunk başlat - overlap için son birkaç cümleyi al
            if overlap_words > 0 and current_chunk:
                # Son cümleleri overlap için sakla
                overlap_text = " ".join(current_chunk)
                overlap_words_actual = len(overlap_text.split())

                if overlap_words_actual > overlap_words:
                    # Overlap için sadece son X kelimeyi al
                    overlap_sents = []
                    temp_words = 0
                    for i in range(len(current_chunk) - 1, -1, -1):
                        sent_words_count = len(current_chunk[i].split())
                        if temp_words + sent_words_count <= overlap_words:
                            overlap_sents.insert(0, current_chunk[i])
                            temp_words += sent_words_count
                        else:
                            break
                    current_chunk = overlap_sents
                    current_word_count = temp_words
                else:
                    current_chunk = current_chunk[:]
                    current_word_count = overlap_words_actual
            else:
                current_chunk = []
                current_word_count = 0

        # Cümleyi ekle
        current_chunk.append(sent)
        current_word_count += sent_word_count
        word_position += sent_word_count

    # Son chunk'ı ekle
    if current_chunk:
        chunk_text = " ".join(current_chunk).strip()
        if len(chunk_text) >= min_chars:
            chunks.append(chunk_text)
            starts.append(word_position - current_word_count)

    return chunks, starts

def process_all_documents(documents, chunk_size_words=300, overlap_words=50):
    """
    Tüm dökümanları chunk'la ve metadata oluştur
    """
    all_chunks = []
    all_metas = []
    today = datetime.now().date().isoformat()

    for doc_id, text in documents.items():
        chunks, starts = chunk_text(text, chunk_size_words, overlap_words)

        for i, (chunk, start) in enumerate(zip(chunks, starts)):
            all_chunks.append(chunk)
            all_metas.append({
                "doc_id": doc_id,
                "chunk_id": len(all_metas),  # Global chunk ID
                "local_chunk_id": i,         # Document içindeki chunk ID
                "start_word_index": start,
                "date": today,
            })

    return all_chunks, all_metas

# -----------------------------------
# 3) Similarity Detection & Deduplication
# -----------------------------------

def detect_similar_chunks(embeddings, similarity_threshold=0.85):
    """
    Benzer chunk'ları tespit et (cosine similarity ile)
    """
    similarity_matrix = embeddings @ embeddings.T
    similar_pairs = []

    for i in range(len(embeddings)):
        for j in range(i+1, len(embeddings)):
            similarity = similarity_matrix[i, j]
            if similarity > similarity_threshold:
                similar_pairs.append((i, j, similarity))

    return similar_pairs

def create_duplicate_groups(similar_pairs):
    """
    Benzer chunk'ları gruplara ayır
    """
    groups = []
    processed = set()

    for i, j, sim in similar_pairs:
        if i in processed or j in processed:
            continue

        # Yeni grup oluştur
        group = {i, j}

        # Bu gruba bağlanabilecek diğer chunk'ları bul
        for x, y, _ in similar_pairs:
            if x in group or y in group:
                group.add(x)
                group.add(y)

        groups.append(list(group))
        processed.update(group)

    return groups

def apply_similarity_penalty(results, duplicate_groups, penalty_factor=0.5):
    """
    Benzer chunk'lara penalty uygula
    """
    # Hangi chunk'ın hangi gruba ait olduğunu map'le
    chunk_to_group = {}
    for group_id, chunk_ids in enumerate(duplicate_groups):
        for chunk_id in chunk_ids:
            chunk_to_group[chunk_id] = group_id

    # Group'lara göre penalty uygula
    group_counts = {}
    adjusted_results = []

    for chunk_id, score in results:
        if chunk_id in chunk_to_group:
            group_id = chunk_to_group[chunk_id]
            group_counts[group_id] = group_counts.get(group_id, 0) + 1

            # Aynı gruptan ne kadar çok chunk seçildiyse o kadar penalty
            penalty = penalty_factor ** (group_counts[group_id] - 1)
            adjusted_score = score * penalty
        else:
            adjusted_score = score

        adjusted_results.append((chunk_id, adjusted_score))

    return adjusted_results

# -----------------------------------
# 4) Document Diversity
# -----------------------------------

def apply_document_diversity(results, metas, max_per_doc=3, diversity_penalty=0.3):
    """
    Her dokümandan maksimum N chunk alarak diversity sağla
    """
    doc_counts = {}
    adjusted_results = []

    for chunk_id, score in results:
        doc_id = metas[chunk_id]['doc_id']
        doc_counts[doc_id] = doc_counts.get(doc_id, 0) + 1

        # Document penalty: Aynı dokümandan ne kadar çok chunk o kadar düşük skor
        if doc_counts[doc_id] <= max_per_doc:
            penalty = 1.0 / (1 + diversity_penalty * (doc_counts[doc_id] - 1))
            adjusted_score = score * penalty
            adjusted_results.append((chunk_id, adjusted_score))

    return adjusted_results

# -----------------------------------
# 5) MMR Selection (Güncellenmiş)
# -----------------------------------

def mmr_select(query_vec, cand_ids, emb_matrix, lambda_mult=0.7, k=10):
    """
    Düzeltilmiş MMR implementasyonu - boyut hatalarını çözer ve unique sonuç garanti eder
    """
    # Input'u unique yap
    unique_cands = []
    seen = set()
    for cid in cand_ids:
        if cid not in seen:
            unique_cands.append(cid)
            seen.add(cid)

    if not unique_cands:
        return []

    # query_vec boyut kontrolü
    if len(query_vec.shape) == 2:
        query_vec = query_vec.squeeze()  # (1,D) -> (D)

    # emb_matrix'in doğru boyutta olduğundan emin ol
    assert emb_matrix.ndim == 2, f"emb_matrix must be (N, D), got shape {emb_matrix.shape}"
    assert len(query_vec.shape) == 1, f"query_vec must be (D,), got shape {query_vec.shape}"
    assert query_vec.shape[0] == emb_matrix.shape[1], f"Dimension mismatch: query {query_vec.shape} vs matrix {emb_matrix.shape}"

    selected = []
    remaining_cands = unique_cands[:]

    # Query ile tüm adayların benzerliğini hesapla
    query_sims = emb_matrix[remaining_cands] @ query_vec  # (N_cands,)

    while remaining_cands and len(selected) < k:
        if not selected:
            # İlk seçim: en yüksek query benzerliği
            best_idx = int(np.argmax(query_sims))
        else:
            # MMR hesapla
            selected_embs = emb_matrix[selected]  # (N_selected, D)

            # Her aday için en yüksek benzerliği selected ile hesapla
            max_similarities = []
            for i, cand_id in enumerate(remaining_cands):
                cand_emb = emb_matrix[cand_id]  # (D,)
                # Seçilmiş tüm chunk'larla benzerlik hesapla
                sims_with_selected = selected_embs @ cand_emb  # (N_selected,)
                max_sim = np.max(sims_with_selected)
                max_similarities.append(max_sim)

            max_similarities = np.array(max_similarities)

            # MMR skoru hesapla
            mmr_scores = lambda_mult * query_sims - (1 - lambda_mult) * max_similarities
            best_idx = int(np.argmax(mmr_scores))

        # En iyi adayı seç
        selected_cand = remaining_cands[best_idx]
        selected.append(selected_cand)

        # Seçilen adayı listelerden çıkar
        remaining_cands.pop(best_idx)
        query_sims = np.delete(query_sims, best_idx)

    return selected

# -----------------------------------------------------
# 6) Embedding (E5 formatı) + FAISS cosine benzeri IP
# -----------------------------------------------------

def build_dense_index(chunks, model_name="intfloat/multilingual-e5-base"):
    model = SentenceTransformer(model_name)
    # E5 için "passage: ..." prefix'i (instruction-tuned)
    embs = model.encode([f"passage: {c}" for c in chunks], normalize_embeddings=True)
    embs = embs.astype("float32")
    index = faiss.IndexFlatIP(embs.shape[1])  # cosine ~ inner product (normed)
    index.add(embs)
    return model, index, embs

def dense_search(query, index, model, topk=30):
    q_vec = model.encode([f"query: {query}"], normalize_embeddings=True).astype("float32")
    D, I = index.search(q_vec, topk)
    return list(zip(I[0].tolist(), D[0].tolist()))  # (idx, score)

# --------------------------------
# 7) BM25 lexical arama
# --------------------------------

def build_bm25(chunks):
    tokenized = [c.lower().split() for c in chunks]
    return BM25Okapi(tokenized)

def bm25_search(query, bm25, topk=30):
    scores = bm25.get_scores(query.lower().split())
    I = np.argsort(scores)[::-1][:topk]
    return list(zip(I.tolist(), scores[I].tolist()))

# --------------------------------
# 8) Reciprocal Rank Fusion (RRF) - Düzeltilmiş
# --------------------------------

def rrf(results_list, k=60, c=60):
    """
    Düzeltilmiş RRF - aynı chunk_id'leri birleştirir
    """
    chunk_scores = {}

    for results in results_list:
        for rank, (chunk_id, score) in enumerate(results[:k], start=1):
            if chunk_id not in chunk_scores:
                chunk_scores[chunk_id] = 0
            chunk_scores[chunk_id] += 1.0 / (c + rank)

    # Skorlara göre sırala ve unique chunk_id'leri döndür
    sorted_chunks = sorted(chunk_scores.items(), key=lambda x: x[1], reverse=True)
    return sorted_chunks

# -------------------------
# 9) Semantic Reranking (CrossEncoder Yerine)
# -------------------------

def semantic_rerank(query, candidate_ids, chunks, model, embeddings, final_k=5, fallback_threshold=0.1):
    """
    Smart semantic reranking with CrossEncoder fallback for low-quality queries
    """
    # Önce geçerli ve unique chunk_id'leri al
    valid_unique_ids = []
    seen = set()
    for cid in candidate_ids:
        if 0 <= cid < len(chunks) and cid not in seen:
            valid_unique_ids.append(cid)
            seen.add(cid)

    if not valid_unique_ids:
        return [], []

    print(f"Semantic rerank'a giren valid unique ids: {valid_unique_ids}")

    # Query embedding
    query_emb = model.encode([f"query: {query}"], normalize_embeddings=True).astype("float32")

    # Her chunk için similarity hesapla
    scores = []
    for cid in valid_unique_ids:
        chunk_emb = embeddings[cid:cid+1]  # (1, D) slice
        similarity = float(chunk_emb @ query_emb.T)
        scores.append(similarity)

    # Score quality check: Eğer tüm skorlar çok yakınsa CrossEncoder fallback
    score_range = max(scores) - min(scores)
    avg_score = sum(scores) / len(scores)

    print(f"Semantic scores - Range: {score_range:.4f}, Average: {avg_score:.4f}")

    # Low-quality query detection - daha conservative threshold
    if score_range < 0.02 and avg_score > 0.6:  # Çok yakın skorlar VE yüksek average = anlamsız query
        print("⚠️  Düşük kalite query tespit edildi, CrossEncoder fallback...")
        try:
            cross = CrossEncoder("cross-encoder/ms-marco-MiniLM-L-6-v2")
            pairs = [(query, chunks[i]) for i in valid_unique_ids]
            cross_scores = cross.predict(pairs, batch_size=32).tolist()

            # CrossEncoder skorlarını kullan
            ranked = sorted(zip(valid_unique_ids, cross_scores), key=lambda x: x[1], reverse=True)
            top_k = ranked[:final_k]
            top_ids = [cid for cid, _ in top_k]
            top_scores = [score for _, score in top_k]

            print("✅ CrossEncoder fallback kullanıldı")
            return top_ids, top_scores

        except Exception as e:
            print(f"CrossEncoder fallback hatası: {e}, semantic skorlar kullanılacak")

    # Normal semantic scoring
    ranked = sorted(zip(valid_unique_ids, scores), key=lambda x: x[1], reverse=True)
    top_k = ranked[:final_k]
    top_ids = [cid for cid, _ in top_k]
    top_scores = [score for _, score in top_k]

    return top_ids, top_scores

def hybrid_rerank(query, candidate_ids, chunks, model, embeddings, cross_model_name=None, final_k=5, batch_size=32):
    """
    Hybrid reranking: Semantic similarity + opsiyonel CrossEncoder
    """
    # Önce geçerli ve unique chunk_id'leri al
    valid_unique_ids = []
    seen = set()
    for cid in candidate_ids:
        if 0 <= cid < len(chunks) and cid not in seen:
            valid_unique_ids.append(cid)
            seen.add(cid)

    if not valid_unique_ids:
        return [], []

    print(f"Hybrid rerank'a giren valid unique ids: {valid_unique_ids}")

    # 1) Semantic similarity skorları
    query_emb = model.encode([f"query: {query}"], normalize_embeddings=True).astype("float32")
    semantic_scores = []
    for cid in valid_unique_ids:
        chunk_emb = embeddings[cid:cid+1]
        similarity = float(chunk_emb @ query_emb.T)
        semantic_scores.append(similarity)

    # 2) CrossEncoder skorları (opsiyonel)
    if cross_model_name:
        try:
            cross = CrossEncoder(cross_model_name)
            pairs = [(query, chunks[i]) for i in valid_unique_ids]
            cross_scores = cross.predict(pairs, batch_size=batch_size).tolist()

            # Normalize cross scores to [0,1] range
            cross_scores = np.array(cross_scores)
            cross_scores = (cross_scores - cross_scores.min()) / (cross_scores.max() - cross_scores.min() + 1e-8)
            cross_scores = cross_scores.tolist()

            # Combine: 70% semantic, 30% cross-encoder
            final_scores = [0.7 * sem + 0.3 * cross for sem, cross in zip(semantic_scores, cross_scores)]
            print("Hybrid scoring: 70% semantic + 30% cross-encoder")
        except Exception as e:
            print(f"CrossEncoder hatası: {e}. Sadece semantic similarity kullanılacak.")
            final_scores = semantic_scores
    else:
        final_scores = semantic_scores
        print("Pure semantic similarity kullanılıyor")

    # Score'a göre sırala
    ranked = sorted(zip(valid_unique_ids, final_scores), key=lambda x: x[1], reverse=True)

    # Final K'yı al
    top_k = ranked[:final_k]
    top_ids = [cid for cid, _ in top_k]
    top_scores = [score for _, score in top_k]

    return top_ids, top_scores

# -------------------------
# 10) Yardımcı yazdırma
# -------------------------

def print_results(query, chunks, metas, top_ids, scores=None, header="Final Results"):
    print("\n" + "=" * 80)
    print(f"{header} (query: {query})")
    print("=" * 80)

    # Document frequency analizi
    doc_freq = {}
    for cid in top_ids:
        doc_id = metas[cid]['doc_id']
        doc_freq[doc_id] = doc_freq.get(doc_id, 0) + 1

    print(f"Document distribution: {doc_freq}")
    print("-" * 80)

    for rank, cid in enumerate(top_ids, start=1):
        meta = metas[cid]
        score_str = f" | score={scores[rank-1]:.4f}" if scores is not None else ""
        print(f"[{rank}] doc={meta['doc_id']} chunk={meta.get('local_chunk_id', cid)} start={meta['start_word_index']}{score_str}")
        print("-" * 80)
        print(chunks[cid])
        print("-" * 80)

# -------------------------
# 11) Argümanlar
# -------------------------

def parse_args():
    ap = argparse.ArgumentParser(description="Multi-Document Hybrid RAG Retrieval with Semantic Reranking")
    ap.add_argument("--docs-folder", required=True, type=str, help="Doküman klasörü veya tek dosya yolu")
    ap.add_argument("--query", required=True, type=str, help="Arama sorusu")
    ap.add_argument("--chunk-size", type=int, default=300, help="Chunk kelime uzunluğu (varsayılan: 300)")
    ap.add_argument("--overlap", type=int, default=50, help="Chunk overlap kelime sayısı (varsayılan: 50)")
    ap.add_argument("--topk", type=int, default=50, help="Dense/BM25 ilk getirilecek aday sayısı")
    ap.add_argument("--final-k", type=int, default=5, help="Rerank sonrası döndürülecek sonuç sayısı")
    ap.add_argument("--similarity-threshold", type=float, default=0.85, help="Similarity detection threshold")
    ap.add_argument("--max-per-doc", type=int, default=3, help="Her dokümandan maksimum chunk sayısı")
    ap.add_argument("--dense-model", type=str, default="intfloat/multilingual-e5-base", help="SentenceTransformer dense model")
    ap.add_argument("--rerank-method", type=str, default="semantic", choices=["semantic", "hybrid"], help="Reranking method")
    ap.add_argument("--cross-model", type=str, default=None, help="CrossEncoder rerank modeli (hybrid method için)")
    return ap.parse_args()

# -------------------------
# 12) Main - Güncellenmiş
# -------------------------

def main():
    args = parse_args()

    # 1) Çoklu doküman yükleme
    documents = load_multiple_documents(args.docs_folder)
    if not documents:
        print("Hata: Hiç doküman yüklenemedi.", file=sys.stderr)
        sys.exit(1)

    # 2) Chunk'la + metadata
    chunks, metas = process_all_documents(documents, chunk_size_words=args.chunk_size, overlap_words=args.overlap)
    print(f"Toplam {len(chunks)} chunk oluşturuldu ({len(documents)} dokümandan).")

    # 3) İndeksleri kur
    dense_model = SentenceTransformer(args.dense_model)
    embs = dense_model.encode([f"passage: {c}" for c in chunks], normalize_embeddings=True).astype("float32")
    index = faiss.IndexFlatIP(embs.shape[1])
    index.add(embs)

    # BM25
    bm25 = build_bm25(chunks)

    # 4) Similarity detection
    similar_pairs = detect_similar_chunks(embs, similarity_threshold=args.similarity_threshold)
    duplicate_groups = create_duplicate_groups(similar_pairs)

    print(f"Tespit edilen benzer chunk grupları: {len(duplicate_groups)}")
    for i, group in enumerate(duplicate_groups):
        print(f"  Grup {i+1}: {group} (similarity > {args.similarity_threshold})")

    # 5) Retrieval (Hybrid + RRF)
    q = args.query
    dense_res = dense_search(q, index=index, model=dense_model, topk=args.topk)
    bm25_res  = bm25_search(q, bm25=bm25, topk=args.topk)

    print(f"Dense search results: {dense_res[:5]}")  # İlk 5'i göster
    print(f"BM25 search results: {bm25_res[:5]}")    # İlk 5'i göster

    # RRF fusion
    fusion_results = rrf([dense_res, bm25_res], k=args.topk, c=60)
    print(f"RRF fusion results: {fusion_results[:5]}")

    candidate_ids = [chunk_id for chunk_id, score in fusion_results[:args.topk]
                    if 0 <= chunk_id < len(chunks)]

    # 6) Similarity penalty uygula
    fusion_with_penalty = apply_similarity_penalty(fusion_results[:args.topk], duplicate_groups, penalty_factor=0.6)
    candidate_ids = [chunk_id for chunk_id, score in fusion_with_penalty
                    if 0 <= chunk_id < len(chunks)]

    # 7) Document diversity uygula
    diverse_results = apply_document_diversity([(cid, 1.0) for cid in candidate_ids], metas, max_per_doc=args.max_per_doc)
    candidate_ids = [chunk_id for chunk_id, score in diverse_results]

    # 8) MMR diversity
    q_vec = dense_model.encode([f"query: {q}"], normalize_embeddings=True).astype("float32")[0]
    candidate_ids = mmr_select(q_vec, candidate_ids, embs, lambda_mult=0.7, k=min(args.topk, len(candidate_ids)))

    print(f"Final candidate selection: {len(candidate_ids)} benzersiz chunk seçildi.")

    # 9) Reranking
    print(f"Rerank öncesi candidate_ids: {candidate_ids}")

    if args.rerank_method == "semantic":
        final_ids, scores = semantic_rerank(q, candidate_ids, chunks, dense_model, embs, final_k=args.final_k)
    elif args.rerank_method == "hybrid":
        final_ids, scores = hybrid_rerank(q, candidate_ids, chunks, dense_model, embs,
                                        cross_model_name=args.cross_model, final_k=args.final_k)

    print(f"Rerank sonrası final_ids: {final_ids}")

    # 10) Sonuçları yazdır
    method_name = f"Multi-Doc RAG + {args.rerank_method.title()} Reranking"
    print_results(q, chunks, metas, final_ids, scores, header=f"Final Results ({method_name})")

    # 11) JSON output
    payload = []
    seen_for_json = set()
    for cid in final_ids:
        if 0 <= cid < len(chunks) and cid not in seen_for_json:
            payload.append({"meta": metas[cid], "text": chunks[cid]})
            seen_for_json.add(cid)

    print("\n" + "=" * 80)
    print("JSON Output:")
    print("=" * 80)
    print(json.dumps(payload, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()