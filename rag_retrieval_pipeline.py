from sentence_transformers import SentenceTransformer, CrossEncoder
import faiss, numpy as np
from rank_bm25 import BM25Okapi

# 1) Embedding & index
emb = SentenceTransformer("intfloat/e5-base-v2")  # TR/multilingual gerekirse gte-multilingual
def enc(texts):
    # E5 için instruction formatı: "query: ...", "passage: ..."
    return emb.encode([f"passage: {t}" for t in texts], normalize_embeddings=True)

chunks = [...]               # metin chunk’ların
metas  = [...]               # her birine {doc_id, section, date, ...}
X = enc(chunks).astype('float32')
index = faiss.IndexFlatIP(X.shape[1]); index.add(X)

# 2) BM25
bm25 = BM25Okapi([c.split() for c in chunks])

# 3) Sorgu zenginleştir
q_raw = "Ödeme entegrasyonunda Sentry ClickHouse neden gerekir?"
q_variants = [
  q_raw,
  "Sentry APM ve event analizi için ClickHouse gereksinimi",
  "Sentry arka uç depolama ClickHouse Kafka mimarisi"
]

# 4) Dense + BM25 al, fusion yap
def dense_search(q, topk=30):
    qv = emb.encode([f"query: {q}"], normalize_embeddings=True).astype('float32')
    D, I = index.search(qv, topk)
    return list(zip(I[0].tolist(), D[0].tolist()))  # (idx, score)

def bm25_search(q, topk=30):
    scores = bm25.get_scores(q.split())
    I = np.argsort(scores)[::-1][:topk]
    return list(zip(I.tolist(), scores[I].tolist()))

def rrf(results_list, k=60, c=60):
    # results_list: [ [(idx,score),...], ... ]  (farklı sorgular ve metotlar)
    rank = {}
    for results in results_list:
        for r, (i, _) in enumerate(results[:k], start=1):
            rank[i] = rank.get(i, 0) + 1.0/(c + r)
    return sorted(rank.items(), key=lambda x: x[1], reverse=True)

all_results = []
for q in q_variants:
    all_results += [dense_search(q, 50), bm25_search(q, 50)]
fusion = rrf(all_results, k=50)
candidate_ids = [i for i,_ in fusion[:50]]

# 5) Rerank (cross-encoder)
reranker = CrossEncoder("cross-encoder/ms-marco-MiniLM-L-6-v2")
pairs = [(q_raw, chunks[i]) for i in candidate_ids]
scores = reranker.predict(pairs)
top = [cid for _, cid in sorted(zip(scores, candidate_ids), reverse=True)[:5]]

final_ctx = [chunks[i] for i in top]
final_meta = [metas[i]  for i in top]
