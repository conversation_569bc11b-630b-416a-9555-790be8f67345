#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
retrieval_demo.py
- Tek bir .txt veya .pdf dokümandan metni okur
- Chunk'lar ve metadata üretir
- Hybrid retrieval (BM25 + Dense FAISS) + RRF fusion uygular
- Cross-Encoder ile rerank edip en iyi parçaları yazdırır

<PERSON>llanım:
  python retrieval_demo.py --path mydoc.pdf --query "ödeme entegrasyonunda sentry clickhouse neden gerekir?" \
      --chunk-size 300 --overlap 50 --topk 50 --final-k 5
"""

import argparse
import json
import sys
from datetime import datetime
from pathlib import Path

import numpy as np

# PDF okumak için
from pypdf import PdfReader

# BM25 (lexical)
from rank_bm25 import BM25Okapi

# Dense embedding + FAISS index
import faiss
from sentence_transformers import SentenceTransformer, CrossEncoder

# ----------------------------
# 1) IO: <PERSON><PERSON>adan metin çekme
# ----------------------------

def read_text(path: Path) -> str:
    if not path.exists():
        raise FileNotFoundError(f"Dosya bulunamadı: {path}")

    if path.suffix.lower() == ".txt":
        return path.read_text(encoding="utf-8", errors="ignore")

    if path.suffix.lower() == ".pdf":
        reader = PdfReader(str(path))
        texts = []
        for page in reader.pages:
            t = page.extract_text() or ""
            texts.append(t)
        return "\n".join(texts)

    raise ValueError(f"Desteklenmeyen dosya türü: {path.suffix} (yalnızca .txt ve .pdf)")

# -----------------------------------
# 2) Geliştirilmiş chunking (cümle sınırlarına saygılı)
# -----------------------------------

import re

_SENT_SPLIT = re.compile(r'(?<=[.!?])\s+')

def chunk_text(text: str, chunk_size_words: int = 300, overlap_words: int = 50,
               min_chars: int = 120):
    """
    Cümle sınırlarına saygılı chunking - cümle ortasından kesmez
    """
    # Cümlelere böl
    sents = [s.strip() for s in _SENT_SPLIT.split(text) if s.strip()]
    chunks, starts = [], []
    current_chunk = []
    current_word_count = 0

    word_position = 0  # Toplam kelime pozisyonu takibi

    for sent in sents:
        sent_words = sent.split()
        sent_word_count = len(sent_words)

        # Eğer bu cümleyi eklemek chunk boyutunu aşarsa
        if current_word_count + sent_word_count > chunk_size_words and current_chunk:
            # Mevcut chunk'ı kaydet
            chunk_text = " ".join(current_chunk).strip()
            if len(chunk_text) >= min_chars:  # Çok kısa chunk'ları önle
                chunks.append(chunk_text)
                starts.append(word_position - current_word_count)

            # Yeni chunk başlat - overlap için son birkaç cümleyi al
            if overlap_words > 0 and current_chunk:
                # Son cümleleri overlap için sakla
                overlap_text = " ".join(current_chunk)
                overlap_words_actual = len(overlap_text.split())

                if overlap_words_actual > overlap_words:
                    # Overlap için sadece son X kelimeyi al
                    overlap_sents = []
                    temp_words = 0
                    for i in range(len(current_chunk) - 1, -1, -1):
                        sent_words_count = len(current_chunk[i].split())
                        if temp_words + sent_words_count <= overlap_words:
                            overlap_sents.insert(0, current_chunk[i])
                            temp_words += sent_words_count
                        else:
                            break
                    current_chunk = overlap_sents
                    current_word_count = temp_words
                else:
                    current_chunk = current_chunk[:]
                    current_word_count = overlap_words_actual
            else:
                current_chunk = []
                current_word_count = 0

        # Cümleyi ekle
        current_chunk.append(sent)
        current_word_count += sent_word_count
        word_position += sent_word_count

    # Son chunk'ı ekle
    if current_chunk:
        chunk_text = " ".join(current_chunk).strip()
        if len(chunk_text) >= min_chars:
            chunks.append(chunk_text)
            starts.append(word_position - current_word_count)

    return chunks, starts


def build_metas(chunks, starts, doc_id: str):
    today = datetime.now().date().isoformat()
    metas = []
    for i, (c, s) in enumerate(zip(chunks, starts)):
        metas.append({
            "doc_id": doc_id,
            "chunk_id": i,
            "start_word_index": s,
            "date": today,
        })
    return metas


def mmr_select(query_vec, cand_ids, emb_matrix, lambda_mult=0.7, k=10):
    """
    Düzeltilmiş MMR implementasyonu - boyut hatalarını çözer
    """
    import numpy as np

    # query_vec boyut kontrolü
    if len(query_vec.shape) == 2:
        query_vec = query_vec.squeeze()  # (1,D) -> (D)

    # emb_matrix'in doğru boyutta olduğundan emin ol
    assert emb_matrix.ndim == 2, f"emb_matrix must be (N, D), got shape {emb_matrix.shape}"
    assert len(query_vec.shape) == 1, f"query_vec must be (D,), got shape {query_vec.shape}"
    assert query_vec.shape[0] == emb_matrix.shape[1], f"Dimension mismatch: query {query_vec.shape} vs matrix {emb_matrix.shape}"

    selected = []
    remaining_cands = cand_ids[:]

    # Query ile tüm adayların benzerliğini hesapla
    query_sims = emb_matrix[remaining_cands] @ query_vec  # (N_cands,)

    while remaining_cands and len(selected) < k:
        if not selected:
            # İlk seçim: en yüksek query benzerliği
            best_idx = int(np.argmax(query_sims))
        else:
            # MMR hesapla
            selected_embs = emb_matrix[selected]  # (N_selected, D)

            # Her aday için en yüksek benzerliği selected ile hesapla
            max_similarities = []
            for i, cand_id in enumerate(remaining_cands):
                cand_emb = emb_matrix[cand_id]  # (D,)
                # Seçilmiş tüm chunk'larla benzerlik hesapla
                sims_with_selected = selected_embs @ cand_emb  # (N_selected,)
                max_sim = np.max(sims_with_selected)
                max_similarities.append(max_sim)

            max_similarities = np.array(max_similarities)

            # MMR skoru hesapla
            mmr_scores = lambda_mult * query_sims - (1 - lambda_mult) * max_similarities
            best_idx = int(np.argmax(mmr_scores))

        # En iyi adayı seç
        selected_cand = remaining_cands[best_idx]
        selected.append(selected_cand)

        # Seçilen adayı listelerden çıkar
        remaining_cands.pop(best_idx)
        query_sims = np.delete(query_sims, best_idx)

    return selected


# -----------------------------------------------------
# 3) Embedding (E5 formatı) + FAISS cosine benzeri IP
# -----------------------------------------------------

def build_dense_index(chunks, model_name="intfloat/multilingual-e5-base"):
    model = SentenceTransformer(model_name)
    # E5 için "passage: ..." prefix'i (instruction-tuned)
    embs = model.encode([f"passage: {c}" for c in chunks], normalize_embeddings=True)
    embs = embs.astype("float32")
    index = faiss.IndexFlatIP(embs.shape[1])  # cosine ~ inner product (normed)
    index.add(embs)
    return model, index, embs

def dense_search(query, index, model, topk=30):
    q_vec = model.encode([f"query: {query}"], normalize_embeddings=True).astype("float32")
    D, I = index.search(q_vec, topk)
    return list(zip(I[0].tolist(), D[0].tolist()))  # (idx, score)

# --------------------------------
# 4) BM25 lexical arama
# --------------------------------

def build_bm25(chunks):
    tokenized = [c.lower().split() for c in chunks]
    return BM25Okapi(tokenized)

def bm25_search(query, bm25, topk=30):
    scores = bm25.get_scores(query.lower().split())
    I = np.argsort(scores)[::-1][:topk]
    return list(zip(I.tolist(), scores[I].tolist()))

# --------------------------------
# 5) Reciprocal Rank Fusion (RRF) - Düzeltilmiş
# --------------------------------

def rrf(results_list, k=60, c=60):
    """
    Düzeltilmiş RRF - aynı chunk_id'leri birleştirir
    """
    chunk_scores = {}

    for results in results_list:
        for rank, (chunk_id, score) in enumerate(results[:k], start=1):
            if chunk_id not in chunk_scores:
                chunk_scores[chunk_id] = 0
            chunk_scores[chunk_id] += 1.0 / (c + rank)

    # Skorlara göre sırala ve unique chunk_id'leri döndür
    sorted_chunks = sorted(chunk_scores.items(), key=lambda x: x[1], reverse=True)
    return sorted_chunks

# -------------------------
# 6) Reranking (CrossEncoder) - Düzeltilmiş
# -------------------------

def rerank(query, candidate_ids, chunks, model_name="cross-encoder/ms-marco-MiniLM-L-6-v2",
          final_k=5, batch_size=32):
    """
    Düzeltilmiş rerank - invalid chunk_id'leri ve duplicate'leri temizler
    """
    # Önce geçerli ve unique chunk_id'leri al
    valid_unique_ids = []
    seen = set()
    for cid in candidate_ids:
        if 0 <= cid < len(chunks) and cid not in seen:
            valid_unique_ids.append(cid)
            seen.add(cid)

    if not valid_unique_ids:
        return [], []

    print(f"Rerank'a giren valid unique ids: {valid_unique_ids}")

    cross = CrossEncoder(model_name)
    pairs = [(query, chunks[i]) for i in valid_unique_ids]
    scores = cross.predict(pairs, batch_size=batch_size).tolist()

    # Skora göre sırala
    ranked = sorted(zip(valid_unique_ids, scores), key=lambda x: x[1], reverse=True)

    # Final K'yı al
    top_k = ranked[:final_k]
    top_ids = [cid for cid, _ in top_k]
    top_scores = [score for _, score in top_k]

    return top_ids, top_scores


# -------------------------
# 7) Yardımcı yazdırma
# -------------------------

def print_results(query, chunks, metas, top_ids, scores=None, header="Final Results"):
    print("\n" + "=" * 80)
    print(f"{header} (query: {query})")
    print("=" * 80)
    for rank, cid in enumerate(top_ids, start=1):
        meta = metas[cid]
        score_str = f" | rerank_score={scores[rank-1]:.4f}" if scores is not None else ""
        print(f"[{rank}] doc_id={meta['doc_id']} chunk_id={meta['chunk_id']} start={meta['start_word_index']}{score_str}")
        print("-" * 80)
        print(chunks[cid])
        print("-" * 80)


# -------------------------
# 8) Argümanlar
# -------------------------

def parse_args():
    ap = argparse.ArgumentParser(description="Hybrid RAG Retrieval Demo (.txt/.pdf)")
    ap.add_argument("--path", required=True, type=str, help="Kaynak doküman yolu (.txt veya .pdf)")
    ap.add_argument("--query", required=True, type=str, help="Arama sorusu")
    ap.add_argument("--chunk-size", type=int, default=300, help="Chunk kelime uzunluğu (varsayılan: 300)")
    ap.add_argument("--overlap", type=int, default=50, help="Chunk overlap kelime sayısı (varsayılan: 50)")
    ap.add_argument("--topk", type=int, default=50, help="Dense/BM25 ilk getirilecek aday sayısı")
    ap.add_argument("--final-k", type=int, default=5, help="Rerank sonrası döndürülecek sonuç sayısı")
    ap.add_argument("--dense-model", type=str, default="intfloat/multilingual-e5-base", help="SentenceTransformer dense model")
    ap.add_argument("--cross-model", type=str, default="cross-encoder/ms-marco-MiniLM-L-6-v2", help="CrossEncoder rerank modeli")
    return ap.parse_args()

# -------------------------
# 9) Main - Düzeltilmiş
# -------------------------

def main():
    args = parse_args()
    path = Path(args.path)
    doc_id = path.stem

    # 1) Oku
    text = read_text(path)
    if not text.strip():
        print("Uyarı: Dokümandan metin çekilemedi veya boş.", file=sys.stderr)
        sys.exit(1)

    # 2) Chunk'la + metadata
    chunks, starts = chunk_text(text, chunk_size_words=args.chunk_size, overlap_words=args.overlap)
    metas = build_metas(chunks, starts, doc_id=doc_id)

    print(f"Toplam {len(chunks)} chunk oluşturuldu.")

    # 3) İndeksleri kur
    # Dense
    dense_model = SentenceTransformer(args.dense_model)
    embs = dense_model.encode([f"passage: {c}" for c in chunks], normalize_embeddings=True).astype("float32")
    index = faiss.IndexFlatIP(embs.shape[1])
    index.add(embs)

    # BM25
    bm25 = build_bm25(chunks)

    # 4) Retrieval (Hybrid + RRF)
    q = args.query
    dense_res = dense_search(q, index=index, model=dense_model, topk=args.topk)
    bm25_res  = bm25_search(q, bm25=bm25, topk=args.topk)

    # RRF fusion - artık unique chunk_id'ler döndürür
    fusion_results = rrf([dense_res, bm25_res], k=args.topk, c=60)
    candidate_ids = [chunk_id for chunk_id, score in fusion_results[:args.topk]]

    # MMR diversity (isteğe bağlı - hata düzeltildi)
    q_vec = dense_model.encode([f"query: {q}"], normalize_embeddings=True).astype("float32")[0]
    candidate_ids = mmr_select(q_vec, candidate_ids, embs, lambda_mult=0.7, k=min(args.topk, len(candidate_ids)))

    print(f"RRF+MMR sonrası {len(candidate_ids)} benzersiz chunk seçildi.")

    # 5) Rerank
    print(f"Rerank öncesi candidate_ids: {candidate_ids}")
    final_ids, scores = rerank(q, candidate_ids, chunks, model_name=args.cross_model, final_k=args.final_k)
    print(f"Rerank sonrası final_ids: {final_ids}")

    # 6) Sonuçları yazdır
    print_results(q, chunks, metas, final_ids, scores, header="Final Results (RRF + MMR + CrossEncoder)")

    # 7) JSON olarak da almak istersen:
    payload = []
    for cid in final_ids:
        payload.append({"meta": metas[cid], "text": chunks[cid]})

    print("\n" + "=" * 80)
    print("JSON Output:")
    print("=" * 80)
    print(json.dumps(payload, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()